const WhatsNew = require('../../models/whatsNew');

/**
 * Get all What's New logs for mobile app
 * Returns logs sorted by creation date (newest first)
 */
exports.getAllLogs = async (req, res) => {
  try {
    console.log('Fetching all What\'s New logs for mobile app');
    
    const logs = await WhatsNew.find()
      .sort({ createdAt: -1 })
      .select('-__v');
    
    console.log(`Found ${logs.length} What's New logs`);
    
    res.status(200).json(logs);
  } catch (error) {
    console.error('Error fetching What\'s New logs:', error);
    res.status(500).json({ 
      message: 'Error fetching What\'s New logs',
      error: error.message 
    });
  }
};

/**
 * Check if there are new logs since last visit
 * Used for showing the red dot indicator on home screen
 */
exports.checkNewLogs = async (req, res) => {
  try {
    const { lastVisit } = req.query;
    
    console.log('Checking for new What\'s New logs since:', lastVisit);
    
    let query = {};
    
    // If lastVisit is provided, check for logs created after that time
    if (lastVisit) {
      const lastVisitDate = new Date(lastVisit);
      if (!isNaN(lastVisitDate.getTime())) {
        query.createdAt = { $gt: lastVisitDate };
      }
    }
    
    const newLogsCount = await WhatsNew.countDocuments(query);
    const hasNewLogs = newLogsCount > 0;
    
    console.log(`Found ${newLogsCount} new logs since last visit`);
    
    res.status(200).json({
      hasNewLogs,
      newLogsCount
    });
  } catch (error) {
    console.error('Error checking for new What\'s New logs:', error);
    res.status(500).json({ 
      message: 'Error checking for new logs',
      error: error.message 
    });
  }
};

/**
 * Get all What's New logs for admin panel
 * Includes additional metadata for admin management
 */
exports.getAllLogsAdmin = async (req, res) => {
  try {
    console.log('Fetching all What\'s New logs for admin panel');
    
    const logs = await WhatsNew.find()
      .sort({ createdAt: -1 });
    
    console.log(`Found ${logs.length} What's New logs for admin`);
    
    res.status(200).json(logs);
  } catch (error) {
    console.error('Error fetching What\'s New logs for admin:', error);
    res.status(500).json({ 
      message: 'Error fetching What\'s New logs',
      error: error.message 
    });
  }
};

/**
 * Create new What's New log (admin only)
 */
exports.createLog = async (req, res) => {
  try {
    const {
      type,
      date,
      title_en,
      title_es,
      title_dom,
      description_en,
      description_es,
      description_dom,
      videoUrl,
      appVersion,
      platform
    } = req.body;

    console.log('Creating new What\'s New log:', { type, title_en, appVersion, platform });

    // Validate required fields
    if (!type || !date || !title_en || !title_es || !title_dom ||
        !description_en || !description_es || !description_dom || !appVersion) {
      return res.status(400).json({
        message: 'Missing required fields: type, date, title_en, title_es, title_dom, description_en, description_es, description_dom, appVersion'
      });
    }

    // Validate type enum
    const validTypes = ['dark', 'red', 'cyan', 'purple'];
    if (!validTypes.includes(type)) {
      return res.status(400).json({
        message: `Invalid type. Must be one of: ${validTypes.join(', ')}`
      });
    }

    // Validate platform enum
    const validPlatforms = ['ios', 'android', 'both'];
    if (platform && !validPlatforms.includes(platform)) {
      return res.status(400).json({
        message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
      });
    }

    // Create new log
    const newLog = new WhatsNew({
      type,
      date,
      title_en,
      title_es,
      title_dom,
      description_en,
      description_es,
      description_dom,
      videoUrl: videoUrl || undefined,
      appVersion,
      platform: platform || 'both'
    });

    const savedLog = await newLog.save();

    console.log('What\'s New log created successfully:', savedLog._id);

    res.status(201).json(savedLog);
  } catch (error) {
    console.error('Error creating What\'s New log:', error);
    res.status(500).json({
      message: 'Error creating What\'s New log',
      error: error.message
    });
  }
};

/**
 * Update existing What's New log (admin only)
 */
exports.updateLog = async (req, res) => {
  try {
    const { id } = req.params;
    const {
      type,
      date,
      title_en,
      title_es,
      title_dom,
      description_en,
      description_es,
      description_dom,
      videoUrl,
      appVersion,
      platform
    } = req.body;

    console.log('Updating What\'s New log:', id);

    // Validate type enum if provided
    if (type) {
      const validTypes = ['dark', 'red', 'cyan', 'purple'];
      if (!validTypes.includes(type)) {
        return res.status(400).json({
          message: `Invalid type. Must be one of: ${validTypes.join(', ')}`
        });
      }
    }

    // Validate platform enum if provided
    if (platform) {
      const validPlatforms = ['ios', 'android', 'both'];
      if (!validPlatforms.includes(platform)) {
        return res.status(400).json({
          message: `Invalid platform. Must be one of: ${validPlatforms.join(', ')}`
        });
      }
    }

    // Find and update the log
    const updatedLog = await WhatsNew.findByIdAndUpdate(
      id,
      {
        ...(type && { type }),
        ...(date && { date }),
        ...(title_en && { title_en }),
        ...(title_es && { title_es }),
        ...(title_dom && { title_dom }),
        ...(description_en && { description_en }),
        ...(description_es && { description_es }),
        ...(description_dom && { description_dom }),
        ...(videoUrl !== undefined && { videoUrl }),
        ...(appVersion && { appVersion }),
        ...(platform && { platform })
      },
      { new: true, runValidators: true }
    );

    if (!updatedLog) {
      return res.status(404).json({ message: 'What\'s New log not found' });
    }

    console.log('What\'s New log updated successfully:', updatedLog._id);

    res.status(200).json(updatedLog);
  } catch (error) {
    console.error('Error updating What\'s New log:', error);
    res.status(500).json({
      message: 'Error updating What\'s New log',
      error: error.message
    });
  }
};

/**
 * Delete What's New log (admin only)
 */
exports.deleteLog = async (req, res) => {
  try {
    const { id } = req.params;
    
    console.log('Deleting What\'s New log:', id);
    
    const deletedLog = await WhatsNew.findByIdAndDelete(id);
    
    if (!deletedLog) {
      return res.status(404).json({ message: 'What\'s New log not found' });
    }
    
    console.log('What\'s New log deleted successfully:', deletedLog._id);
    
    res.status(200).json({ 
      message: 'What\'s New log deleted successfully',
      deletedLog 
    });
  } catch (error) {
    console.error('Error deleting What\'s New log:', error);
    res.status(500).json({ 
      message: 'Error deleting What\'s New log',
      error: error.message 
    });
  }
};
