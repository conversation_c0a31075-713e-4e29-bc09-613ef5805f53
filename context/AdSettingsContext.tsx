/**
 * Ad Settings Context
 *
 * Manages ad settings state and provides access to ad configuration
 * throughout the app. Settings are loaded at app launch and cached locally.
 */

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AppState, AppStateStatus, Platform } from 'react-native';
import { loadAdSettings, fetchAndUpdateAdSettings, AdSettings } from '@/services/adSettingsService';
import { sessionStorageService } from '@/services/sessionStorageService';

interface AdSettingsContextProps {
  // State
  adSettings: AdSettings | null;
  isLoading: boolean;
  lastUpdated: number | null;

  // Actions
  refreshAdSettings: (forceRefresh?: boolean) => Promise<void>;

  // Convenience getters
  isWatchAdsButtonEnabled: boolean;
  requiredAdsCount: number;
  freeContentLimit: number;
}

// No default values - everything must come from API

const AdSettingsContext = createContext<AdSettingsContextProps>({
  // No default values - everything must come from API
  adSettings: null as any,
  isLoading: true,
  lastUpdated: null,

  // Default functions
  refreshAdSettings: async () => {},

  // No default getters - everything must come from API
  isWatchAdsButtonEnabled: false,
  requiredAdsCount: 0,
  freeContentLimit: 0,
});

export const AdSettingsProvider = ({ children }: { children: ReactNode }) => {
  const [adSettings, setAdSettings] = useState<AdSettings | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [lastUpdated, setLastUpdated] = useState<number | null>(null);

  // Load ad settings on mount
  useEffect(() => {
    loadAdSettingsOnMount();
  }, []);

  // Update session storage service when ad settings change
  useEffect(() => {
    if (adSettings) {
      sessionStorageService.setRequiredAdsCount(adSettings.requiredAdsCount);
      sessionStorageService.setFreeContentLimit(adSettings.freeContentLimit);
    }
  }, [adSettings]);

  // Handle app state changes - refresh settings when app becomes active
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active') {
        // Refresh settings when app becomes active (respects throttling)
        refreshAdSettings(false);
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription.remove();
  }, []);

  const loadAdSettingsOnMount = async () => {
    try {
      setIsLoading(true);
      console.log('AdSettingsContext: Loading ad settings on mount');

      const settings = await loadAdSettings();
      setAdSettings(settings);
      setLastUpdated(Date.now());

      console.log('AdSettingsContext: Ad settings loaded successfully:', settings);
    } catch (error) {
      console.error('AdSettingsContext: Error loading ad settings on mount:', error);
      // Keep default settings on error
    } finally {
      setIsLoading(false);
    }
  };

  const refreshAdSettings = async (forceRefresh: boolean = false) => {
    try {
      if (isLoading) {
        console.log('AdSettingsContext: Already loading, skipping refresh');
        return;
      }

      setIsLoading(true);
      console.log('AdSettingsContext: Refreshing ad settings, forceRefresh:', forceRefresh);

      // Use fetchAndUpdateAdSettings directly to respect the forceRefresh parameter
      const settings = await fetchAndUpdateAdSettings(forceRefresh);
      setAdSettings(settings);
      setLastUpdated(Date.now());

      console.log('AdSettingsContext: Ad settings refreshed successfully:', settings);
    } catch (error) {
      console.error('AdSettingsContext: Error refreshing ad settings:', error);
      // Keep current settings on error
    } finally {
      setIsLoading(false);
    }
  };

  // Convenience getters with OS-specific logic
  const isWatchAdsButtonEnabled = Platform.OS === 'ios'
    ? (adSettings?.watchAdsButtonEnabledIOS ?? adSettings?.watchAdsButtonEnabled ?? false)
    : (adSettings?.watchAdsButtonEnabledAndroid ?? adSettings?.watchAdsButtonEnabled ?? false);
  const requiredAdsCount = adSettings?.requiredAdsCount || 0;
  const freeContentLimit = adSettings?.freeContentLimit || 0;

  const contextValue: AdSettingsContextProps = {
    // State
    adSettings,
    isLoading,
    lastUpdated,

    // Actions
    refreshAdSettings,

    // Convenience getters
    isWatchAdsButtonEnabled,
    requiredAdsCount,
    freeContentLimit,
  };

  return (
    <AdSettingsContext.Provider value={contextValue}>
      {children}
    </AdSettingsContext.Provider>
  );
};

export const useAdSettings = () => {
  const context = useContext(AdSettingsContext);
  if (!context) {
    throw new Error('useAdSettings must be used within an AdSettingsProvider');
  }
  return context;
};

export default AdSettingsContext;
